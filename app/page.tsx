import type { Metadata } from 'next'
import NeonHeroBox from "@/components/neon-hero-box"
import { getCanonicalUrl } from '@/lib/utils'
import Script from 'next/script'

export const metadata: Metadata = {
  title: 'Free AI Resume Screening Software - The Consult Now | Best AI Recruitment Tool 2025',
  description: 'Best free AI resume screening software for 2025. Transform hiring with intelligent candidate matching, bias detection, and instant resume analysis. Start free trial today!',
  keywords: 'free AI resume screening software, best AI resume screening, AI recruitment software, free resume screening tool, AI candidate matching, resume parser free, AI hiring software 2025, best free recruitment software',
  authors: [{ name: 'The Consult Now Team' }],
  creator: 'The Consult Now',
  publisher: 'The Consult Now',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: getCanonicalUrl('/'),
  },
  openGraph: {
    title: 'Free AI Resume Screening Software - The Consult Now | Best AI Recruitment Tool 2025',
    description: 'Best free AI resume screening software for 2025. Transform hiring with intelligent candidate matching, bias detection, and instant resume analysis. Start free trial today!',
    url: getCanonicalUrl('/'),
    siteName: 'The Consult Now',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Free AI Resume Screening Software - The Consult Now | Best AI Recruitment Tool 2025',
    description: 'Best free AI resume screening software for 2025. Transform hiring with intelligent candidate matching, bias detection, and instant resume analysis. Start free trial today!',
    creator: '@theconsultnow',
  },
}

export default function Home() {
  // Organization structured data
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "The Consult Now",
    "url": "https://www.theconsultnow.com",
    "logo": "https://www.theconsultnow.com/images/logo.svg",
    "description": "AI-powered resume screening and job matching platform that transforms hiring processes with intelligent candidate evaluation.",
    "foundingDate": "2024",
    "sameAs": [
      "https://twitter.com/theconsultnow",
      "https://linkedin.com/company/theconsultnow"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "url": "https://www.theconsultnow.com/support"
    },
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "US"
    }
  }

  // Software Application schema
  const softwareSchema = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "The Consult Now",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web Browser",
    "description": "AI-powered resume screening software that helps recruiters and HR teams find the best candidates quickly and efficiently.",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "description": "Free tier available with premium options"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "150"
    },
    "featureList": [
      "AI Resume Screening",
      "Candidate Matching",
      "Bias Detection",
      "CSV Export",
      "GDPR Compliance"
    ]
  }

  return (
    <>
      {/* Organization Schema */}
      <Script
        id="organization-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }}
      />

      {/* Software Application Schema */}
      <Script
        id="software-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(softwareSchema) }}
      />

      <NeonHeroBox />
    </>
  )
}
