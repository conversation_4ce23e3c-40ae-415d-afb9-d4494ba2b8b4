import type { Metada<PERSON> } from 'next'
import './globals.css'
import { Toaster } from "@/components/ui/sonner"
import { ThemeProvider } from "@/components/theme-provider"
import { AppLayout } from "@/components/app-layout"
import { AnalyticsProvider } from "@/components/analytics-provider"
import { <PERSON>ieConsent } from "@/components/cookie-consent"
import Script from 'next/script'
import { GA_TRACKING_ID } from '@/lib/gtag'
import { getCanonicalUrl } from '@/lib/utils'

export const metadata: Metadata = {
  title: 'The Consult Now - AI-Powered Resume Screening & Job Matching',
  description: 'Transform your hiring process with AI-powered resume screening. Match candidates to job descriptions instantly with our intelligent ATS platform.',
  keywords: 'AI resume screening, resume screening software, AI recruitment, candidate matching, ATS platform, free resume screening, AI hiring tools',
  authors: [{ name: 'The Consult Now Team' }],
  creator: 'The Consult Now',
  publisher: 'The Consult Now',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: getCanonicalUrl('/'),
  },
  openGraph: {
    title: 'The Consult Now - AI-Powered Resume Screening & Job Matching',
    description: 'Transform your hiring process with AI-powered resume screening. Match candidates to job descriptions instantly with our intelligent ATS platform.',
    url: getCanonicalUrl('/'),
    siteName: 'The Consult Now',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Consult Now - AI-Powered Resume Screening & Job Matching',
    description: 'Transform your hiring process with AI-powered resume screening. Match candidates to job descriptions instantly with our intelligent ATS platform.',
    creator: '@theconsultnow',
  },

}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#1aa8e0" />
        <meta name="format-detection" content="telephone=no" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-icon.svg" />
        <link rel="manifest" href="/manifest.json" />

        {/* Google Analytics - Conditional Loading */}
        <Script id="gtag-consent-init" strategy="beforeInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}

            // Initialize consent state to denied by default
            gtag('consent', 'default', {
              'analytics_storage': 'denied'
            });
          `}
        </Script>

        <Script
          src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}
          strategy="afterInteractive"
        />

        <Script id="google-analytics" strategy="afterInteractive">
          {`
            gtag('js', new Date());
            gtag('config', '${GA_TRACKING_ID}', {
              page_path: window.location.pathname,
            });
          `}
        </Script>
      </head>
      <body className="bg-black text-white">
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={false} forcedTheme="dark">
          <AnalyticsProvider>
            <AppLayout>
              {children}
            </AppLayout>
            <Toaster />
            <CookieConsent />
          </AnalyticsProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
