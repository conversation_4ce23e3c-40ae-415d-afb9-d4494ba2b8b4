"use client"

import React, { useState, useEffect, FormEvent, ChangeEvent } from "react"
import Link from "next/link"
import Image from "next/image"
import { Mail, Lock, Loader2 } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"

// Firebase imports
import { auth, db } from "@/lib/firebase"
import { signInWithEmailAndPassword, GoogleAuthProvider, signInWithPopup } from "firebase/auth"
import { doc, getDoc, setDoc } from "firebase/firestore"
import { migrateConsentToFirebase } from "@/lib/consent-manager"

// Add JSX namespace to fix JSX element type errors
declare global {
  namespace JSX {
    interface IntrinsicElements {
      [elemName: string]: any;
    }
  }
}

export default function LoginForm() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const router = useRouter()
  const { toast } = useToast()

  // Handle client-side only code
  useEffect(() => {
    setIsMounted(true)
  }, [])

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!email.trim()) newErrors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(email)) newErrors.email = "Email is invalid";
    if (!password) newErrors.password = "Password is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!validateForm()) return;

    setIsLoading(true)
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password)
      console.log("✅ Logged in user:", userCredential.user)

      // Migrate consent from localStorage to Firebase if exists
      await migrateConsentToFirebase(userCredential.user.uid);

      toast({
        title: "Login successful",
        description: "You've been logged in successfully!",
      });

      router.push("/start-analysing")
    } catch (error: any) {
      console.error("❌ Login error:", error.message)

      toast({
        title: "Login failed",
        description: "Invalid credentials. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false)
    }
  }

  const handleGoogleSignIn = async () => {
    setIsLoading(true)
    const provider = new GoogleAuthProvider()

    try {
      const result = await signInWithPopup(auth, provider)
      const user = result.user
      console.log("✅ Google login success:", user)

      // Check if user exists in Firestore, if not create a record
      const userDoc = await getDoc(doc(db, "users", user.uid))
      if (!userDoc.exists()) {
        // Create user record for new Google users
        await setDoc(doc(db, "users", user.uid), {
          fullName: user.displayName || "",
          companyName: "", // Will be collected later in profile
          email: user.email,
          profession: "google-oauth",
          createdAt: new Date(),
          registrationDate: new Date(),
          tier: "Free",
          jobDescriptionCount: 0,
          lastRenewalDate: new Date(),
        })
      }

      // Migrate consent from localStorage to Firebase if exists
      await migrateConsentToFirebase(user.uid);

      toast({
        title: "Login successful",
        description: "You've been logged in with Google!",
      });

      router.push("/start-analysing")
    } catch (error: any) {
      console.error("❌ Google login error:", error.code, error.message)

      let errorMessage = "Google sign-in failed. Please try again.";

      // Handle specific Firebase Auth errors
      if (error.code === 'auth/popup-closed-by-user') {
        errorMessage = "Sign-in popup was closed before completing the process.";
      } else if (error.code === 'auth/popup-blocked') {
        errorMessage = "Sign-in popup was blocked by your browser. Please allow popups for this site.";
      } else if (error.code === 'auth/cancelled-popup-request') {
        errorMessage = "Multiple popup requests were triggered. Please try again.";
      } else if (error.code === 'auth/network-request-failed') {
        errorMessage = "Network error occurred. Please check your internet connection.";
      } else if (error.code === 'auth/unauthorized-domain') {
        errorMessage = "This domain is not authorized for OAuth operations. Contact support.";
      } else if (error.code === 'auth/operation-not-allowed') {
        errorMessage = "Google sign-in is not enabled. Contact support.";
      } else if (error.code === 'auth/account-exists-with-different-credential') {
        errorMessage = "An account already exists with the same email but different sign-in credentials.";
      }

      toast({
        title: "Login failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false)
    }
  }

  // Don't render the form until client-side hydration is complete
  if (!isMounted) {
    return (
      <div className="w-full h-screen flex items-center justify-center bg-gray-900">
        <div className="h-8 w-8 rounded-full border-2 border-t-transparent border-[#8529db] animate-spin" />
      </div>
    );
  }

  return (
    <div className="w-full h-screen flex flex-col md:flex-row">
      {/* Left side - Login form (opposite of sign-up page) */}
      <div className="w-full md:w-1/2 bg-gray-900 p-8 md:p-12 flex flex-col justify-center">
        <div className="mx-auto w-full max-w-md">
          {/* Back to About link */}
          <button
            onClick={() => router.push("/")}
            className="text-gray-400 hover:text-white transition-colors mb-6 flex items-center gap-2 text-sm"
          >
            ← About
          </button>
          <h2 className="text-2xl text-white mb-8 font-normal">Login</h2>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="relative">
              <Input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e: ChangeEvent<HTMLInputElement>) => setEmail(e.target.value)}
                className={`pl-12 bg-transparent border ${errors.email ? "border-red-500" : "border-gray-700"} rounded-sm focus:border-[#8529db] transition-colors text-white`}
                disabled={isLoading}
                required
              />
              <Mail className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
              {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
            </div>

            <div className="relative">
              <Input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e: ChangeEvent<HTMLInputElement>) => setPassword(e.target.value)}
                className={`pl-12 bg-transparent border ${errors.password ? "border-red-500" : "border-gray-700"} rounded-sm focus:border-[#8529db] transition-colors text-white`}
                disabled={isLoading}
                required
              />
              <Lock className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
              {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password}</p>}
            </div>

            <div className="flex justify-end">
              <Link href="/forgot-password" className="text-[#1aa8e0] text-sm hover:underline">
                Forgot Password?
              </Link>
            </div>

            <Button
              type="submit"
              className="w-full rounded-full font-normal text-white"
              style={{ background: "#8529db" }}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Logging in...
                </>
              ) : (
                "Login"
              )}
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-gray-400">Or</p>

            <button
              onClick={handleGoogleSignIn}
              className="mt-4 flex items-center justify-center space-x-2 w-full border border-gray-700 rounded-full py-2 px-4 hover:bg-gray-800 transition-colors"
              disabled={isLoading}
              type="button"
            >
              <Image src="/images/google-logo.png" alt="Google" width={20} height={20} />
              <span className="text-white font-normal">
                {isLoading ? "Processing..." : "Login with Google"}
              </span>
            </button>
          </div>

          <p className="text-gray-400 text-center mt-6">
            Don't have an account?{" "}
            <Link href="/signup" className="text-[#1aa8e0] hover:underline">
              Sign Up
            </Link>
          </p>
        </div>
      </div>

      {/* Right side with purple background (opposite of sign-up page) */}
      <div
        className="w-full md:w-1/2 p-8 md:p-12 flex flex-col justify-center relative"
        style={{
          background: "#8529db",
        }}
      >
        {/* Dark triangle overlay matching left side background */}
        <div
          className="absolute top-0 left-0 bottom-0 w-full h-full bg-gray-900"
          style={{
            clipPath: "polygon(0 0, 0 100%, 40% 100%)",
            zIndex: 10,
          }}
        ></div>

        <div className="flex justify-center w-full relative z-20">
          <h1 className="text-3xl md:text-4xl text-white mb-4 font-normal text-center">Welcome Back!</h1>
        </div>
      </div>
    </div>
  )
}
