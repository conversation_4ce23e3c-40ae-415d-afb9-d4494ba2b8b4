'use client';

import { Suspense, useEffect, useState } from 'react';
import { useAnalytics } from '@/hooks/use-analytics';
import { hasAnalyticsConsent } from '@/lib/consent-manager';
import { useAuthState } from 'react-firebase-hooks/auth';
import { auth } from '@/lib/firebase';

function AnalyticsWrapper() {
  const [user] = useAuthState(auth);

  // Always call useAnalytics - the gtag functions will handle consent checking
  useAnalytics();

  // Initialize consent state on mount and when user changes
  useEffect(() => {
    const initializeConsent = async () => {
      try {
        const hasConsent = await hasAnalyticsConsent(user?.uid);

        // Initialize gtag consent state
        if (typeof window !== 'undefined' && window.gtag) {
          window.gtag('consent', 'default', {
            'analytics_storage': hasConsent ? 'granted' : 'denied'
          });
        }
      } catch (error) {
        console.error('Error checking analytics consent:', error);
        // Default to denied on error
        if (typeof window !== 'undefined' && window.gtag) {
          window.gtag('consent', 'default', {
            'analytics_storage': 'denied'
          });
        }
      }
    };

    initializeConsent();
  }, [user]);

  return null;
}

export function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  return (
    <>
      <Suspense fallback={null}>
        <AnalyticsWrapper />
      </Suspense>
      {children}
    </>
  );
}
