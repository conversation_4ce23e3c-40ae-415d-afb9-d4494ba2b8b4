'use client';

import { Suspense, useEffect, useState } from 'react';
import { useAnalytics } from '@/hooks/use-analytics';
import { hasAnalyticsConsent } from '@/lib/consent-manager';
import { useAuthState } from 'react-firebase-hooks/auth';
import { auth } from '@/lib/firebase';

function AnalyticsWrapper() {
  const [user] = useAuthState(auth);
  const [consentGranted, setConsentGranted] = useState(false);
  const [consentChecked, setConsentChecked] = useState(false);

  // Check consent status
  useEffect(() => {
    const checkConsent = async () => {
      try {
        const hasConsent = await hasAnalyticsConsent(user?.uid);
        setConsentGranted(hasConsent);

        // Initialize gtag consent state
        if (typeof window !== 'undefined' && window.gtag) {
          window.gtag('consent', 'default', {
            'analytics_storage': hasConsent ? 'granted' : 'denied'
          });
        }
      } catch (error) {
        console.error('Error checking analytics consent:', error);
        setConsentGranted(false);
      } finally {
        setConsentChecked(true);
      }
    };

    checkConsent();
  }, [user]);

  // Only use analytics if consent is granted and checked
  const shouldUseAnalytics = consentChecked && consentGranted;

  // Conditionally use analytics
  if (shouldUseAnalytics) {
    useAnalytics();
  }

  return null;
}

export function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  return (
    <>
      <Suspense fallback={null}>
        <AnalyticsWrapper />
      </Suspense>
      {children}
    </>
  );
}
