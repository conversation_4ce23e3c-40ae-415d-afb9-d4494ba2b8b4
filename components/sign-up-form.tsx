"use client";

import React, { useState, useEffect, FormEvent, ChangeEvent } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { User, Mail, Lock, Briefcase, Building, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { trackSignup } from "@/lib/gtag";

// Firebase Auth and Firestore
import { auth, db } from "@/lib/firebase";
import { createUserWithEmailAndPassword, GoogleAuthProvider, signInWithPopup } from "firebase/auth";
import { setDoc, doc } from "firebase/firestore";
import { migrateConsentToFirebase } from "@/lib/consent-manager";

// Add JSX namespace to fix JSX element type errors
declare global {
  namespace JSX {
    interface IntrinsicElements {
      [elemName: `data-${string}`]: any;
    }
  }
}

export default function SignUpForm() {
  const [fullName, setFullName] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [profession, setProfession] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  // Handle client-side only code
  useEffect(() => {
    setIsMounted(true)
  }, []);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!fullName.trim()) newErrors.fullName = "Name is required";
    if (!companyName.trim()) newErrors.companyName = "Company name is required";
    if (!profession) newErrors.profession = "Profession is required";
    if (!email.trim()) newErrors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(email)) newErrors.email = "Email is invalid";
    if (!password) newErrors.password = "Password is required";
    else if (password.length < 6) newErrors.password = "Password must be at least 6 characters";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      console.log("✅ User signed up:", user);

      // Store user data in Firestore with usage tracking
      await setDoc(doc(db, "users", user.uid), {
        fullName,
        companyName,
        email: user.email,
        profession,
        createdAt: new Date(),
        registrationDate: new Date(),
        tier: "Free",
        jobDescriptionCount: 0,
        lastRenewalDate: new Date(),
      });

      // Migrate consent from localStorage to Firebase
      await migrateConsentToFirebase(user.uid);

      // Track signup event with profession
      trackSignup(profession, 'email');

      toast({
        title: "Account created",
        description: "You've successfully signed up!",
      });

      // Redirect to start analysing page
      router.push("/start-analysing");
    } catch (error: any) {
      console.error("❌ Sign-up error:", error.message);
      toast({
        title: "Sign-up failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    const provider = new GoogleAuthProvider();

    try {
      setIsLoading(true);
      const result = await signInWithPopup(auth, provider);
      const user = result.user;

      console.log("✅ Signed in with Google:", user);

      // Store user data in Firestore with usage tracking
      await setDoc(doc(db, "users", user.uid), {
        fullName: user.displayName || "",
        companyName: "", // Will be collected later in profile
        email: user.email,
        profession: "google-oauth", // Or collect later
        createdAt: new Date(),
        registrationDate: new Date(),
        tier: "Free",
        jobDescriptionCount: 0,
        lastRenewalDate: new Date(),
      });

      // Migrate consent from localStorage to Firebase
      await migrateConsentToFirebase(user.uid);

      // Track Google signup
      trackSignup('google-oauth', 'google');

      toast({
        title: "Success",
        description: "Signed in successfully with Google!",
      });

      // Redirect to start analysing page after successful sign-in
      router.push("/start-analysing");
    } catch (error: any) {
      console.error("❌ Google Sign-In error:", error.code, error.message);

      let errorMessage = "Google sign-in failed. Please try again.";

      // Handle specific Firebase Auth errors
      if (error.code === 'auth/popup-closed-by-user') {
        errorMessage = "Sign-in popup was closed before completing the process.";
      } else if (error.code === 'auth/popup-blocked') {
        errorMessage = "Sign-in popup was blocked by your browser. Please allow popups for this site.";
      } else if (error.code === 'auth/cancelled-popup-request') {
        errorMessage = "Multiple popup requests were triggered. Please try again.";
      } else if (error.code === 'auth/network-request-failed') {
        errorMessage = "Network error occurred. Please check your internet connection.";
      } else if (error.code === 'auth/unauthorized-domain') {
        errorMessage = "This domain is not authorized for OAuth operations. Contact support.";
      } else if (error.code === 'auth/operation-not-allowed') {
        errorMessage = "Google sign-in is not enabled. Contact support.";
      } else if (error.code === 'auth/account-exists-with-different-credential') {
        errorMessage = "An account already exists with the same email but different sign-in credentials.";
      }

      toast({
        title: "Google sign-in failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Don't render the form until client-side hydration is complete
  if (!isMounted) {
    return (
      <div className="w-full h-screen flex items-center justify-center bg-gray-900">
        <div className="h-8 w-8 rounded-full border-2 border-t-transparent border-[#8529db] animate-spin" />
      </div>
    );
  }

  return (
    <div className="w-full h-screen flex flex-col md:flex-row">
      {/* Left - Visual */}
      <div
        className="w-full md:w-1/2 p-8 md:p-12 flex flex-col justify-center relative"
        style={{ background: "#8529db" }}
      >
        <div
          className="absolute top-0 right-0 bottom-0 w-full h-full bg-gray-900"
          style={{
            clipPath: "polygon(100% 0, 60% 100%, 100% 100%)",
            zIndex: 10,
          }}
        />
        <div className="flex justify-center w-full relative z-20">
          <h1 className="text-3xl md:text-4xl text-white mb-4 font-normal text-center">
            Create Your Account!
          </h1>
        </div>
      </div>

      {/* Right - Form */}
      <div className="w-full md:w-1/2 bg-gray-900 p-8 md:p-12 flex flex-col justify-center">
        <div className="mx-auto w-full max-w-md">
          {/* Back to About link */}
          <button
            onClick={() => router.push("/")}
            className="text-gray-400 hover:text-white transition-colors mb-6 flex items-center gap-2 text-sm"
          >
            ← About
          </button>
          <h2 className="text-2xl text-white mb-8 font-normal">Sign Up</h2>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="relative">
              <Input
                type="text"
                placeholder="Full Name"
                value={fullName}
                onChange={(e: ChangeEvent<HTMLInputElement>) => setFullName(e.target.value)}
                className={`pl-12 bg-transparent border ${errors.fullName ? "border-red-500" : "border-gray-700"} rounded-sm focus:border-[#8529db] transition-colors text-white`}
                disabled={isLoading}
              />
              <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
              {errors.fullName && <p className="text-red-500 text-xs mt-1">{errors.fullName}</p>}
            </div>

            <div className="relative">
              <Input
                type="text"
                placeholder="Company Name"
                value={companyName}
                onChange={(e: ChangeEvent<HTMLInputElement>) => setCompanyName(e.target.value)}
                className={`pl-12 bg-transparent border ${errors.companyName ? "border-red-500" : "border-gray-700"} rounded-sm focus:border-[#8529db] transition-colors text-white`}
                disabled={isLoading}
              />
              <Building className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
              {errors.companyName && <p className="text-red-500 text-xs mt-1">{errors.companyName}</p>}
            </div>

            <div className="relative">
              <Select value={profession} onValueChange={setProfession} disabled={isLoading}>
                <SelectTrigger className={`pl-12 bg-transparent border ${errors.profession ? "border-red-500" : "border-gray-700"} rounded-sm focus:border-[#8529db] transition-colors text-white data-[placeholder]:text-[#64738b]`}>
                  <SelectValue placeholder="Profession" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="recruiter">Recruiters</SelectItem>
                  <SelectItem value="ceo">CEO</SelectItem>
                  <SelectItem value="founder">Startup Founders</SelectItem>
                  <SelectItem value="freelancer">Freelancers</SelectItem>
                  <SelectItem value="university">University Career Services</SelectItem>
                  <SelectItem value="other">Others</SelectItem>
                </SelectContent>
              </Select>
              <Briefcase className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5 z-10" />
              {errors.profession && <p className="text-red-500 text-xs mt-1">{errors.profession}</p>}
            </div>

            <div className="relative">
              <Input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e: ChangeEvent<HTMLInputElement>) => setEmail(e.target.value)}
                className={`pl-12 bg-transparent border ${errors.email ? "border-red-500" : "border-gray-700"} rounded-sm focus:border-[#8529db] transition-colors text-white`}
                disabled={isLoading}
              />
              <Mail className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
              {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
            </div>

            <div className="relative">
              <Input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e: ChangeEvent<HTMLInputElement>) => setPassword(e.target.value)}
                className={`pl-12 bg-transparent border ${errors.password ? "border-red-500" : "border-gray-700"} rounded-sm focus:border-[#8529db] transition-colors text-white`}
                disabled={isLoading}
              />
              <Lock className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
              {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password}</p>}
            </div>

            <Button
              type="submit"
              className="w-full rounded-full font-normal text-white"
              style={{ background: "#8529db" }}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Signing up...
                </>
              ) : (
                "Sign Up"
              )}
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-gray-400">Or</p>

            <button
              onClick={handleGoogleSignIn}
              className="mt-4 flex items-center justify-center space-x-2 w-full border border-gray-700 rounded-full py-2 px-4 hover:bg-gray-800 transition-colors"
              disabled={isLoading}
            >
              <Image src="/images/google-logo.png" alt="Google" width={20} height={20} />
              <span className="text-white font-normal">Sign up with Google</span>
            </button>
          </div>

          <p className="text-gray-400 text-center mt-6">
            Already have an account?{" "}
            <Link href="/login" className="text-[#1aa8e0] hover:underline">
              Login
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
