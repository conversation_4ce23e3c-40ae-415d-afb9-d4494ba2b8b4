"use client"

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { <PERSON>, <PERSON><PERSON> } from 'lucide-react'
import { auth, db } from '@/lib/firebase'
import { doc, updateDoc, getDoc } from 'firebase/firestore'
import { useAuthState } from 'react-firebase-hooks/auth'

interface CookieConsentProps {
  onConsentChange?: (hasConsent: boolean) => void
}

export function CookieConsent({ onConsentChange }: CookieConsentProps) {
  const [user] = useAuthState(auth)
  const [showBanner, setShowBanner] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Check consent status on component mount
  useEffect(() => {
    checkConsentStatus()
  }, [user])

  const checkConsentStatus = async () => {
    try {
      let hasConsent = false
      let consentExpiry = null

      if (user) {
        // Check Firebase for authenticated users
        const userDoc = await getDoc(doc(db, 'users', user.uid))
        if (userDoc.exists()) {
          const userData = userDoc.data()
          hasConsent = userData.cookieConsent === true
          consentExpiry = userData.cookieConsentExpiry?.toDate()
        }
      } else {
        // Check localStorage for anonymous users
        const localConsent = localStorage.getItem('cookieConsent')
        const localExpiry = localStorage.getItem('cookieConsentExpiry')
        
        if (localConsent && localExpiry) {
          hasConsent = localConsent === 'true'
          consentExpiry = new Date(localExpiry)
        }
      }

      // Check if consent has expired (12 months)
      const now = new Date()
      const isExpired = consentExpiry && now > consentExpiry

      if (!hasConsent || isExpired) {
        setShowBanner(true)
      } else {
        // User has valid consent
        onConsentChange?.(hasConsent)
      }
    } catch (error) {
      console.error('Error checking consent status:', error)
      // Show banner on error to be safe
      setShowBanner(true)
    }
  }

  const saveConsentChoice = async (consent: boolean) => {
    setIsLoading(true)
    
    try {
      // Set expiry date to 12 months from now
      const expiryDate = new Date()
      expiryDate.setFullYear(expiryDate.getFullYear() + 1)

      if (user) {
        // Save to Firebase for authenticated users
        await updateDoc(doc(db, 'users', user.uid), {
          cookieConsent: consent,
          cookieConsentExpiry: expiryDate,
          cookieConsentDate: new Date()
        })
      } else {
        // Save to localStorage for anonymous users
        localStorage.setItem('cookieConsent', consent.toString())
        localStorage.setItem('cookieConsentExpiry', expiryDate.toISOString())
        localStorage.setItem('cookieConsentDate', new Date().toISOString())
      }

      // Hide banner and notify parent component
      setShowBanner(false)
      onConsentChange?.(consent)

      // If user rejected, ensure GA4 is disabled
      if (!consent && typeof window !== 'undefined') {
        // Disable Google Analytics
        if (window.gtag) {
          window.gtag('consent', 'update', {
            'analytics_storage': 'denied'
          })
        }
      }

    } catch (error) {
      console.error('Error saving consent choice:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAccept = () => {
    saveConsentChoice(true)
  }

  const handleReject = () => {
    saveConsentChoice(false)
  }

  // Don't render if banner shouldn't be shown
  if (!showBanner) {
    return null
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-gray-900 border-t border-purple-500/30 shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          
          {/* Cookie Icon and Message */}
          <div className="flex items-start gap-3 flex-1">
            <div className="flex-shrink-0 mt-1">
              <Cookie className="h-5 w-5 text-purple-400" />
            </div>
            <div className="flex-1">
              <p className="text-sm text-gray-300 leading-relaxed">
                We use cookies to enhance your experience and analyze website usage with Google Analytics. 
                Your privacy is important to us. You can choose to accept or reject non-essential cookies.
                <Link 
                  href="/privacy-policy" 
                  className="text-purple-400 hover:text-purple-300 underline ml-1"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Learn more in our Privacy Policy
                </Link>
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-3 flex-shrink-0">
            <Button
              onClick={handleReject}
              disabled={isLoading}
              variant="outline"
              size="sm"
              className="border-gray-600 text-gray-300 hover:bg-gray-800 hover:text-white"
            >
              Reject
            </Button>
            <Button
              onClick={handleAccept}
              disabled={isLoading}
              size="sm"
              className="bg-purple-600 hover:bg-purple-700 text-white border-purple-600"
            >
              {isLoading ? 'Saving...' : 'Accept'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

// Hook to get current consent status
export function useCookieConsent() {
  const [user] = useAuthState(auth)
  const [hasConsent, setHasConsent] = useState<boolean | null>(null)

  useEffect(() => {
    const checkConsent = async () => {
      try {
        if (user) {
          const userDoc = await getDoc(doc(db, 'users', user.uid))
          if (userDoc.exists()) {
            const userData = userDoc.data()
            const consent = userData.cookieConsent === true
            const expiry = userData.cookieConsentExpiry?.toDate()
            
            // Check if expired
            if (expiry && new Date() > expiry) {
              setHasConsent(null) // Expired, need new consent
            } else {
              setHasConsent(consent)
            }
          } else {
            setHasConsent(null)
          }
        } else {
          const localConsent = localStorage.getItem('cookieConsent')
          const localExpiry = localStorage.getItem('cookieConsentExpiry')
          
          if (localConsent && localExpiry) {
            const expiry = new Date(localExpiry)
            if (new Date() > expiry) {
              setHasConsent(null) // Expired
            } else {
              setHasConsent(localConsent === 'true')
            }
          } else {
            setHasConsent(null)
          }
        }
      } catch (error) {
        console.error('Error checking consent:', error)
        setHasConsent(null)
      }
    }

    checkConsent()
  }, [user])

  return hasConsent
}

// Migrate consent from localStorage to Firebase when user signs up
export const migrateConsentToFirebase = async (userId: string) => {
  try {
    const localConsent = localStorage.getItem('cookieConsent')
    const localExpiry = localStorage.getItem('cookieConsentExpiry')
    const localDate = localStorage.getItem('cookieConsentDate')

    if (localConsent && localExpiry) {
      await updateDoc(doc(db, 'users', userId), {
        cookieConsent: localConsent === 'true',
        cookieConsentExpiry: new Date(localExpiry),
        cookieConsentDate: localDate ? new Date(localDate) : new Date()
      })

      // Clear localStorage after migration
      localStorage.removeItem('cookieConsent')
      localStorage.removeItem('cookieConsentExpiry')
      localStorage.removeItem('cookieConsentDate')
    }
  } catch (error) {
    console.error('Error migrating consent to Firebase:', error)
  }
}
