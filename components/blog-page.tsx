"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON>, Calendar, Clock, ArrowRight, User } from "lucide-react"
import { useRouter } from "next/navigation"
import { getAllBlogPosts } from "@/lib/blog-data"


export default function BlogPage() {
  const router = useRouter()
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  const handleGetStarted = () => {
    router.push("/signup")
  }

  const blogPosts = getAllBlogPosts()
  const featuredPost = blogPosts.find(post => post.featured) || blogPosts[0]
  const nonFeaturedPosts = blogPosts.filter(post => !post.featured)



  return (
    <div className="min-h-screen bg-black">
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-72 h-72 bg-[#8529db] rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-[#1aa8e0] rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-[#64738b] rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Header */}
      <header className="relative z-10 bg-black/60 backdrop-blur-xl border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-8 py-6">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <div className="flex items-center gap-3 cursor-pointer" onClick={scrollToTop}>
              <div className="w-8 h-8 flex items-center justify-center">
                <img src="/images/logo-small.svg" alt="The Consult Now Logo" className="w-8 h-8" />
              </div>
              <span className="text-white font-bold text-xl tracking-wide">The Consult Now</span>
            </div>

            {/* Navigation */}
            <nav className="flex items-center gap-6">
              <a href="/#about" className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                About
              </a>
              <a href="/blog" className="text-white font-medium cursor-pointer">
                Blog
              </a>
              <a href="/pricing" className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                Pricing
              </a>
              <a href="/signup" className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                Log in / Sign up
              </a>
            </nav>
          </div>
        </div>
      </header>

      {/* Featured Post */}
      <section className="relative z-10 max-w-7xl mx-auto px-8 py-20 mb-20">
        <div className="bg-black/40 backdrop-blur-xl border border-[#8529db]/30 rounded-2xl overflow-hidden hover:border-[#8529db]/60 transition-all duration-300">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
            <div className="relative h-80 lg:h-auto">
              <img
                src={featuredPost.image || "/placeholder.svg"}
                alt={featuredPost.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute top-6 left-6">
                <Badge className="bg-gradient-to-r from-[#8529db] to-[#1aa8e0] text-white border-0 px-4 py-2">Featured</Badge>
              </div>
            </div>
            <div className="p-10 lg:p-16 flex flex-col justify-center">
              <div className="flex flex-wrap items-center gap-6 mb-6">
                <Badge variant="outline" className="border-[#1aa8e0] text-[#1aa8e0] px-3 py-1">
                  {featuredPost.category}
                </Badge>
                <div className="flex items-center gap-2 text-gray-400 text-sm">
                  <Calendar className="w-4 h-4" />
                  {featuredPost.date}
                </div>
                <div className="flex items-center gap-2 text-gray-400 text-sm">
                  <Clock className="w-4 h-4" />
                  {featuredPost.readTime}
                </div>
              </div>
              <h2 className="text-3xl lg:text-4xl font-bold text-white mb-6 leading-tight">{featuredPost.title}</h2>
              <p className="text-gray-300 mb-8 leading-relaxed text-lg">{featuredPost.excerpt}</p>
              <div className="flex items-center justify-between mt-8">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-[#8529db] to-[#1aa8e0] rounded-full flex items-center justify-center">
                    <User className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-gray-300 text-lg">{featuredPost.author}</span>
                </div>
                <Button
                  onClick={() => router.push(`/blog/${featuredPost.slug}`)}
                  className="bg-transparent border border-[#1aa8e0] text-[#1aa8e0] hover:bg-[#1aa8e0] hover:text-white px-6 py-3"
                >
                  Read More
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Blog Grid */}
      <section className="relative z-10 max-w-7xl mx-auto px-8 mb-24">
        <div className="flex justify-between items-center mb-12">
          <div>
            <h2 className="text-3xl font-bold text-white">Latest Articles</h2>
            <p className="text-gray-400 mt-2">
              {nonFeaturedPosts.length} articles
            </p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
          {nonFeaturedPosts.map((post) => (
            <article
              key={post.id}
              onClick={() => router.push(`/blog/${post.slug}`)}
              className="bg-black/40 backdrop-blur-xl border border-gray-700/30 rounded-xl overflow-hidden hover:border-[#1aa8e0]/60 transition-all duration-300 group cursor-pointer"
            >
              <div className="relative h-56 overflow-hidden">
                <img
                  src={post.image || "/placeholder.svg"}
                  alt={post.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <Badge variant="outline" className="border-[#8529db] text-[#8529db] bg-black/50 backdrop-blur-sm px-3 py-1">
                    {post.category}
                  </Badge>
                </div>
              </div>
              <div className="p-8">
                <div className="flex items-center gap-4 mb-4 text-sm text-gray-400">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    {post.date}
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    {post.readTime}
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-white mb-4 leading-tight group-hover:text-[#1aa8e0] transition-colors">
                  {post.title}
                </h3>
                <p className="text-gray-400 text-base mb-6 leading-relaxed">{post.excerpt}</p>
                <div className="flex items-center justify-between mt-6">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-[#8529db] to-[#1aa8e0] rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-gray-400 text-base">{post.author}</span>
                  </div>
                  <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-[#1aa8e0] transition-colors" />
                </div>
              </div>
            </article>
          ))}
        </div>






      </section>



      {/* Footer - Same as homepage */}
      <footer className="relative z-10 bg-black/60 backdrop-blur-xl border-t border-gray-800 mt-10">
        <div className="max-w-7xl mx-auto px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Company Info */}
            <div className="md:col-span-2">
              <div className="flex items-center gap-2 mb-4 cursor-pointer" onClick={scrollToTop}>
                <div className="w-8 h-8 bg-gradient-to-r from-[#8529db] to-[#1aa8e0] rounded flex items-center justify-center">
                  <Sparkles className="w-4 h-4 text-white" />
                </div>
                <span className="text-white font-bold text-xl tracking-wide">The Consult Now</span>
              </div>
              <p className="text-gray-400 text-sm leading-relaxed max-w-md">
                Cut manual screening by 99% and find the perfect candidates instantly.
              </p>
            </div>

            {/* Product Links */}
            <div>
              <h3 className="text-white font-semibold mb-4">Product</h3>
              <ul className="space-y-2">
                <li>
                  <a
                    href="/#features"
                    className="text-gray-400 hover:text-white transition-colors text-sm cursor-pointer"
                  >
                    Features
                  </a>
                </li>
                <li>
                  <a href="/blog" className="text-gray-400 hover:text-white transition-colors text-sm cursor-pointer">
                    Blog
                  </a>
                </li>
                <li>
                  <a href="/#about" className="text-gray-400 hover:text-white transition-colors text-sm cursor-pointer">
                    About
                  </a>
                </li>
              </ul>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="border-t border-gray-800 mt-8 pt-6">
            <p className="text-gray-500 text-sm text-center">© 2025 The Consult Now. All rights reserved.</p>
          </div>
        </div>
      </footer>

      <style jsx>{`
        .clean-box {
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
      `}</style>
    </div>
  )
}