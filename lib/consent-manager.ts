import { auth, db } from '@/lib/firebase'
import { doc, updateDoc, getDoc } from 'firebase/firestore'

export interface ConsentData {
  hasConsent: boolean
  expiryDate: Date
  consentDate: Date
}

/**
 * Get consent status for current user (authenticated or anonymous)
 */
export async function getConsentStatus(userId?: string): Promise<ConsentData | null> {
  try {
    if (userId) {
      // Check Firebase for authenticated users
      const userDoc = await getDoc(doc(db, 'users', userId))
      if (userDoc.exists()) {
        const userData = userDoc.data()
        
        if (userData.cookieConsent !== undefined && userData.cookieConsentExpiry) {
          return {
            hasConsent: userData.cookieConsent === true,
            expiryDate: userData.cookieConsentExpiry.toDate(),
            consentDate: userData.cookieConsentDate?.toDate() || new Date()
          }
        }
      }
    } else {
      // Check localStorage for anonymous users
      const localConsent = localStorage.getItem('cookieConsent')
      const localExpiry = localStorage.getItem('cookieConsentExpiry')
      const localDate = localStorage.getItem('cookieConsentDate')
      
      if (localConsent && localExpiry) {
        return {
          hasConsent: localConsent === 'true',
          expiryDate: new Date(localExpiry),
          consentDate: localDate ? new Date(localDate) : new Date()
        }
      }
    }
    
    return null
  } catch (error) {
    console.error('Error getting consent status:', error)
    return null
  }
}

/**
 * Check if consent is valid (exists and not expired)
 */
export function isConsentValid(consentData: ConsentData | null): boolean {
  if (!consentData) return false
  
  const now = new Date()
  return now <= consentData.expiryDate
}

/**
 * Save consent choice for authenticated or anonymous user
 */
export async function saveConsentChoice(consent: boolean, userId?: string): Promise<void> {
  try {
    // Set expiry date to 12 months from now
    const expiryDate = new Date()
    expiryDate.setFullYear(expiryDate.getFullYear() + 1)
    const consentDate = new Date()

    if (userId) {
      // Save to Firebase for authenticated users
      await updateDoc(doc(db, 'users', userId), {
        cookieConsent: consent,
        cookieConsentExpiry: expiryDate,
        cookieConsentDate: consentDate
      })
    } else {
      // Save to localStorage for anonymous users
      localStorage.setItem('cookieConsent', consent.toString())
      localStorage.setItem('cookieConsentExpiry', expiryDate.toISOString())
      localStorage.setItem('cookieConsentDate', consentDate.toISOString())
    }

    // Handle Google Analytics consent
    if (typeof window !== 'undefined' && window.gtag) {
      if (consent) {
        window.gtag('consent', 'update', {
          'analytics_storage': 'granted'
        })
      } else {
        window.gtag('consent', 'update', {
          'analytics_storage': 'denied'
        })
      }
    }

  } catch (error) {
    console.error('Error saving consent choice:', error)
    throw error
  }
}

/**
 * Migrate consent from localStorage to Firebase when user signs up
 */
export async function migrateConsentToFirebase(userId: string): Promise<void> {
  try {
    const localConsent = localStorage.getItem('cookieConsent')
    const localExpiry = localStorage.getItem('cookieConsentExpiry')
    const localDate = localStorage.getItem('cookieConsentDate')

    if (localConsent && localExpiry) {
      await updateDoc(doc(db, 'users', userId), {
        cookieConsent: localConsent === 'true',
        cookieConsentExpiry: new Date(localExpiry),
        cookieConsentDate: localDate ? new Date(localDate) : new Date()
      })

      // Clear localStorage after successful migration
      localStorage.removeItem('cookieConsent')
      localStorage.removeItem('cookieConsentExpiry')
      localStorage.removeItem('cookieConsentDate')
      
      console.log('✅ Consent migrated to Firebase successfully')
    }
  } catch (error) {
    console.error('❌ Error migrating consent to Firebase:', error)
    // Don't throw error to avoid breaking user signup flow
  }
}

/**
 * Clear all consent data (for testing or user request)
 */
export async function clearConsentData(userId?: string): Promise<void> {
  try {
    if (userId) {
      // Clear from Firebase
      await updateDoc(doc(db, 'users', userId), {
        cookieConsent: null,
        cookieConsentExpiry: null,
        cookieConsentDate: null
      })
    }
    
    // Clear from localStorage
    localStorage.removeItem('cookieConsent')
    localStorage.removeItem('cookieConsentExpiry')
    localStorage.removeItem('cookieConsentDate')
    
  } catch (error) {
    console.error('Error clearing consent data:', error)
    throw error
  }
}

/**
 * Check if user needs to provide consent (no consent or expired)
 */
export async function needsConsent(userId?: string): Promise<boolean> {
  const consentData = await getConsentStatus(userId)
  return !isConsentValid(consentData)
}

/**
 * Get analytics consent status specifically
 */
export async function hasAnalyticsConsent(userId?: string): Promise<boolean> {
  const consentData = await getConsentStatus(userId)
  return isConsentValid(consentData) && consentData.hasConsent
}

// Type declarations for gtag
declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event' | 'consent',
      targetId: string | 'update',
      config?: {
        page_path?: string
        analytics_storage?: 'granted' | 'denied'
        [key: string]: any
      }
    ) => void
    dataLayer: any[]
  }
}
