// lib/gtag.js
export const GA_TRACKING_ID = 'G-GTGJ791LYW';

// Check if analytics consent is granted
const hasAnalyticsConsent = () => {
  if (typeof window === 'undefined') return false;

  // Check if gtag and dataLayer exist
  if (window.gtag && window.dataLayer) {
    // Check the current consent state from dataLayer
    const consentState = window.dataLayer.find(item =>
      item[0] === 'consent' && item[1] === 'default'
    );

    if (consentState && consentState[2]) {
      return consentState[2].analytics_storage === 'granted';
    }
  }

  return false;
};

// Track pageviews (only if consent granted)
export const pageview = (url) => {
  if (typeof window !== 'undefined' && window.gtag && hasAnalyticsConsent()) {
    window.gtag('config', GA_TRACKING_ID, {
      page_path: url,
    });
  }
};

// Track custom events (only if consent granted)
export const event = ({ action, category, label, value }) => {
  if (typeof window !== 'undefined' && window.gtag && hasAnalyticsConsent()) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};

// Track user signup with profession
export const trackSignup = (profession, method = 'email') => {
  event({
    action: 'sign_up',
    category: 'User',
    label: `${method}_signup_${profession}`,
  });

  // Track profession separately for better analytics
  event({
    action: 'user_profession',
    category: 'Demographics',
    label: profession,
  });
};

// Track resume upload
export const trackResumeUpload = (fileType, fileSize, success = true) => {
  event({
    action: 'upload_resume',
    category: 'Resume',
    label: `${fileType}_${success ? 'success' : 'failed'}`,
    value: Math.round(fileSize / 1024), // Size in KB
  });
};

// Track job creation
export const trackJobCreation = (industry, experienceLevel) => {
  event({
    action: 'create_job',
    category: 'Job',
    label: `${industry}_${experienceLevel}`,
  });
};

// Track analysis events
export const trackAnalysisStart = (resumeCount, processingType = 'batch') => {
  event({
    action: 'analysis_start',
    category: 'Analysis',
    label: `${processingType}_${resumeCount}_resumes`,
    value: resumeCount,
  });
};

export const trackAnalysisComplete = (resumeCount, processingTime, processingType = 'batch') => {
  event({
    action: 'analysis_complete',
    category: 'Analysis',
    label: `${processingType}_${resumeCount}_resumes`,
    value: Math.round(processingTime / 1000), // Time in seconds
  });
};

// Track user actions on candidates
export const trackCandidateAction = (action, matchScore, candidateIndex) => {
  event({
    action: `candidate_${action}`,
    category: 'Candidate',
    label: `${action}_score_${Math.round(matchScore)}`,
    value: candidateIndex,
  });
};

// Track feature usage
export const trackFeatureUsage = (feature, details = '') => {
  event({
    action: 'feature_usage',
    category: 'Features',
    label: `${feature}${details ? `_${details}` : ''}`,
  });
};

// Track errors
export const trackError = (errorType, errorMessage, page) => {
  event({
    action: 'error',
    category: 'Errors',
    label: `${errorType}_${page}`,
  });
};

// Track performance metrics
export const trackPerformance = (metric, value, page) => {
  event({
    action: 'performance',
    category: 'Performance',
    label: `${metric}_${page}`,
    value: Math.round(value),
  });
};

// Track feedback events
export const trackFeedbackSubmitted = (rating, hasFeedbackText) => {
  event({
    action: 'feedback_submitted',
    category: 'Feedback',
    label: `rating_${rating}_${hasFeedbackText ? 'with_text' : 'rating_only'}`,
    value: rating,
  });
};

export const trackFeedbackSkipped = () => {
  event({
    action: 'feedback_skipped',
    category: 'Feedback',
    label: 'first_job_analysis',
  });
};

export const trackFeedbackPopupShown = () => {
  event({
    action: 'feedback_popup_shown',
    category: 'Feedback',
    label: 'first_job_analysis',
  });
};

// SEO-specific tracking events
export const trackBlogRead = (articleTitle, readTime) => {
  event({
    action: 'blog_read',
    category: 'Content',
    label: articleTitle,
    value: readTime,
  });
};

export const trackSearchQuery = (query, source = 'internal') => {
  event({
    action: 'search',
    category: 'SEO',
    label: `${source}_${query}`,
  });
};

export const trackPricingView = (plan) => {
  event({
    action: 'view_pricing',
    category: 'Conversion',
    label: plan,
  });
};

export const trackCTAClick = (ctaText, location) => {
  event({
    action: 'cta_click',
    category: 'Conversion',
    label: `${ctaText}_${location}`,
  });
};

export const trackPageScroll = (percentage, page) => {
  event({
    action: 'scroll',
    category: 'Engagement',
    label: `${page}_${percentage}%`,
    value: percentage,
  });
};
